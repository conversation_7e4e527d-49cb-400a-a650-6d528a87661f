'use client'

import { But<PERSON> } from 'react-aria-components'
import {
    classes,
    filterFieldClasses,
    getCategory,
    getComponentsNotInConfig,
    getFieldName,
    getFieldsNotInConfig,
    getGroupFields,
    getNoCheckedStatus,
    getTabTitle,
    getYesCheckedStatus,
    isCategorised,
    isFileField,
    isInLevel,
    sortCustomisedComponentFields,
    subCategoryVisibilityCheck,
} from '../../actions'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import { LogBookConfiguration } from '@/app/lib/logbook-configuration/types'
import toast from 'react-hot-toast'
import { ITab } from '../../config'
import NavigationTabItem from './navigation-tab-item'
import DocumentField from './document-field'
import { SeaLogsButton } from '@/app/ui/daily-checks/Components'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import ComponentsNotInConfig from './components-not-in-config'
import { useMemo } from 'react'
import {
    CheckFieldContent,
    CheckFieldTopContent,
} from '@/components/daily-check-field'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/app/lib/utils'

interface IComponentProps {
    tabs: ITab[]
    tab: string
    changeTab: Function
    logBookConfig: any
    slallFields: LogBookConfiguration[]
    imCrew: boolean
    deactivateCustomisedComponent: Function
    mapConfigToDefault: Function
    activateCustomisedComponent: Function
    dailyCheckCategories: any
    dailyCheckCategory: any
    changeDailyChecksCategoryTab: Function
    levelThreeCategories: any
    levelThreeCategory: any
    changeLevelThreeCategoryTab: Function
    deactivateCustomisedSubComponent: Function
    activateCustomisedSubComponent: Function
    deactivateCustomisedLevelThreeComponent: Function
    activateCustomisedLevelThreeComponent: Function
    customisedComponentFieldsCombinedFilter: Function
    checkLevelThree: (field: any) => boolean
    setCurrentField: Function
    setOpenConfigEditDialog: Function
    handleEditorChange: Function
    setDescriptionPanelContent: Function
    setOpenDescriptionPanel: Function
    setDescriptionPanelHeading: Function
    isRequiredField: Function
    updatedFields: any
    updatedLocalFields: any
    updateFieldStatus: Function
    documents: Array<Record<string, any>>
    setDocuments: Function
    filterFieldsWithGroup: Function
    checkLevelThreeGroup: Function
    createCustomisedComponent: Function
}

export default function LogbookConfigurationForm({
    tabs,
    tab,
    changeTab,
    logBookConfig,
    slallFields,
    imCrew,
    deactivateCustomisedComponent,
    mapConfigToDefault,
    activateCustomisedComponent,
    dailyCheckCategories,
    dailyCheckCategory,
    changeDailyChecksCategoryTab,
    levelThreeCategories,
    levelThreeCategory,
    changeLevelThreeCategoryTab,
    deactivateCustomisedSubComponent,
    activateCustomisedSubComponent,
    deactivateCustomisedLevelThreeComponent,
    activateCustomisedLevelThreeComponent,
    customisedComponentFieldsCombinedFilter,
    checkLevelThree,
    setCurrentField,
    setOpenConfigEditDialog,
    handleEditorChange,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    setDescriptionPanelHeading,
    isRequiredField,
    updatedFields,
    updatedLocalFields,
    updateFieldStatus,
    documents,
    setDocuments,
    filterFieldsWithGroup,
    checkLevelThreeGroup,
    createCustomisedComponent,
}: IComponentProps) {
    const filteredTabs = useMemo(() => {
        return tabs.filter(
            (element) =>
                element.title !== 'Crew Welfare' &&
                element.title !== 'Crew Training' &&
                element.componentClass !== 'Engine_LogBookComponent' &&
                element.componentClass !== 'Engineer_LogBookComponent' &&
                element.componentClass !== 'Fuel_LogBookComponent' &&
                element.componentClass !== 'Supernumerary_LogBookComponent',
        )
    }, [tabs])

    return (
        <div className="">
            <ul className="border-t border-cool-grey-300 flex mt-2 pt-2 px-4 flex-wrap  font-medium text-center ">
                <Tabs defaultValue={tab}>
                    <TabsList className="flex flex-col lg:flex-row lg:w-[1000px] h-[200px] lg:h-auto pb-2">
                        {filteredTabs.map((element, index: number) => {
                            return (
                                <div
                                    className="w-full"
                                    key={`${element.title}-${index}`}>
                                    <TabsTrigger
                                        value={getTabTitle(
                                            element,
                                            SLALL_LogBookFields,
                                        )}>
                                        <Button
                                            onPress={changeTab(element.title)}>
                                            {getTabTitle(
                                                element,
                                                SLALL_LogBookFields,
                                            )}
                                        </Button>
                                    </TabsTrigger>
                                </div>
                            )
                        })}
                    </TabsList>
                </Tabs>
            </ul>
            {logBookConfig.customisedLogBookComponents?.nodes
                // Crew Members must come first before Crew Welfare
                .sort((a: any, b: any) => {
                    if (a.componentClass === 'CrewMembers_LogBookComponent')
                        return -1
                    if (b.componentClass === 'CrewMembers_LogBookComponent')
                        return 1
                    return 0
                })
                .map((component: any) => (
                    <div
                        key={component.id}
                        className={`border-t mt-2 grid grid-cols-3 pb-2 border-cool-grey-300 ${tab?.replace('Logbook', 'LogBook') === component.title.replace('Logbook', 'LogBook') || (tab === 'Crew Members' && component.title === 'Crew Welfare') || (tab === 'Engine Reports' && (component.componentClass === 'Engineer_LogBookComponent' || component.componentClass === 'Fuel_LogBookComponent')) ? '' : 'hidden'}`}>
                        <div className="flex flex-row md:flex-col items-center md:items-start justify-between col-span-3 md:col-span-1">
                            <h2 className=" p-4 mt-2 ">
                                {getTabTitle(component, SLALL_LogBookFields)}{' '}
                                {component.active ? (
                                    ''
                                ) : (
                                    <span className=""> - Inactive </span>
                                )}
                            </h2>
                            {!isCategorised(
                                component,
                                slallFields,
                                logBookConfig,
                            ) &&
                                !imCrew && (
                                    <div className="mt-2 p-4">
                                        {component.active ? (
                                            <>
                                                {/*<SeaLogsButton
                                                    text="Disable"
                                                    type="secondary"
                                                    color="rose"
                                                    action={deactivateCustomisedComponent(
                                                        component.id,
                                                    )}
                                                />*/}
                                                <Button
                                                    onPress={deactivateCustomisedComponent(
                                                        component.id,
                                                    )}
                                                    className={
                                                        'border border-destructive text-destructive hover:bg-red-vivid-50 rounded-md px-5 py-2'
                                                    }>
                                                    Disable
                                                </Button>
                                                <SeaLogsButton
                                                    type="secondary"
                                                    text="Reset default"
                                                    color="sky"
                                                    action={() =>
                                                        mapConfigToDefault(
                                                            component,
                                                        )
                                                    }
                                                />
                                            </>
                                        ) : (
                                            <SeaLogsButton
                                                text="Enable"
                                                type="secondary"
                                                color="sky"
                                                action={activateCustomisedComponent(
                                                    component.id,
                                                )}
                                            />
                                        )}
                                    </div>
                                )}
                        </div>
                        {isCategorised(
                            component,
                            slallFields,
                            logBookConfig,
                        ) && (
                            <>
                                <div className="col-span-3">
                                    {dailyCheckCategories && (
                                        <ul className="border-t border-cool-grey-300 flex mt-1.5 p-4 flex-wrap  font-medium text-center ">
                                            <Tabs
                                                defaultValue={
                                                    dailyCheckCategory
                                                }>
                                                <TabsList className="flex flex-col lg:flex-row lg:w-[1400px] h-[200px] lg:h-auto pb-2">
                                                    {dailyCheckCategories.map(
                                                        (category: any) => (
                                                            <div
                                                                className="w-full"
                                                                key={category}>
                                                                <TabsTrigger
                                                                    value={
                                                                        category !==
                                                                        'Engine Checks'
                                                                            ? category
                                                                            : 'Engine, steering, electrical & alt power'
                                                                    }>
                                                                    <Button
                                                                        onPress={changeDailyChecksCategoryTab(
                                                                            category,
                                                                        )}>
                                                                        {category !==
                                                                        'Engine Checks'
                                                                            ? category
                                                                            : 'Engine, steering, electrical & alt power'}
                                                                    </Button>
                                                                </TabsTrigger>
                                                            </div>
                                                        ),
                                                    )}
                                                </TabsList>
                                            </Tabs>
                                        </ul>
                                    )}
                                </div>
                                {levelThreeCategories && (
                                    <div
                                        className={`col-span-3 ${isInLevel(dailyCheckCategory, 3, slallFields) ? '' : 'hidden'}`}>
                                        <ul className="border-t border-cool-grey-300 flex mt-1.5 p-4 flex-wrap  font-medium text-center ">
                                            <Tabs defaultValue={tab}>
                                                <TabsList className="flex flex-col lg:flex-row lg:w-[600px] h-[200px] lg:h-auto pb-2">
                                                    {levelThreeCategories.map(
                                                        (category: any) => (
                                                            <div
                                                                className="w-full"
                                                                key={
                                                                    category.label
                                                                }>
                                                                <TabsTrigger
                                                                    value={
                                                                        category.label
                                                                    }>
                                                                    <Button
                                                                        onPress={changeLevelThreeCategoryTab(
                                                                            category.label,
                                                                        )}>
                                                                        {
                                                                            category.label
                                                                        }
                                                                    </Button>
                                                                </TabsTrigger>
                                                            </div>
                                                        ),
                                                    )}
                                                </TabsList>
                                            </Tabs>
                                        </ul>
                                    </div>
                                )}
                                {dailyCheckCategories &&
                                    dailyCheckCategories.map(
                                        (category: any, index: number) => (
                                            <div
                                                key={index}
                                                className={`flex flex-row md:flex-col items-center md:items-start justify-between col-span-3 md:col-span-1 ${dailyCheckCategory === category ? '' : 'hidden'}`}>
                                                <h2 className=" p-4 mt-2 ">
                                                    {category !==
                                                    'Engine Checks'
                                                        ? category
                                                        : 'Engine, steering, electrical & alt power'}{' '}
                                                    {component.active ? (
                                                        ''
                                                    ) : (
                                                        <span className="">
                                                            {' '}
                                                            - Inactive{' '}
                                                        </span>
                                                    )}
                                                </h2>
                                                {category !==
                                                'Engine Checks' ? (
                                                    <div className="mt-2 p-4">
                                                        {(component.subFields ==
                                                            null ||
                                                            (component?.subFields &&
                                                                component.subFields
                                                                    .split('||')
                                                                    .includes(
                                                                        category,
                                                                    ))) &&
                                                        !imCrew ? (
                                                            <>
                                                                {/*<SeaLogsButton
                                                                    text="Disable"
                                                                    type="secondary"
                                                                    color="rose"
                                                                    action={deactivateCustomisedSubComponent(
                                                                        component,
                                                                        category,
                                                                    )}
                                                                />*/}
                                                                <Button
                                                                    onPress={deactivateCustomisedSubComponent(
                                                                        component,
                                                                        category,
                                                                    )}
                                                                    className={
                                                                        'border border-destructive text-destructive hover:bg-red-vivid-50 rounded-md px-5 py-2'
                                                                    }>
                                                                    Disable
                                                                </Button>
                                                            </>
                                                        ) : (
                                                            <>
                                                                {!imCrew && (
                                                                    <>
                                                                        {/*<SeaLogsButton
                                                                            text="Enable"
                                                                            type="secondary"
                                                                            color="sky"
                                                                            action={activateCustomisedSubComponent(
                                                                                component,
                                                                                category,
                                                                            )}
                                                                        />*/}
                                                                        <Button
                                                                            onPress={activateCustomisedSubComponent(
                                                                                component,
                                                                                category,
                                                                            )}
                                                                            className={
                                                                                'border rounded-[6px] bg-light-blue-vivid-900 border-light-blue-vivid-900 text-popover shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-background hover:text-light-blue-vivid-900 px-5 py-2'
                                                                            }>
                                                                            Enable
                                                                        </Button>
                                                                    </>
                                                                )}
                                                            </>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="mt-2 p-4">
                                                        {/* Level Three Categories Enable/Disable Buttons */}
                                                        {levelThreeCategories &&
                                                            levelThreeCategories.map(
                                                                (
                                                                    levelThree: any,
                                                                ) =>
                                                                    levelThree.label ===
                                                                        levelThreeCategory && (
                                                                        <div
                                                                            key={
                                                                                levelThree.label
                                                                            }
                                                                            className="mb-4">
                                                                            {component?.subFields &&
                                                                            component.subFields
                                                                                .split(
                                                                                    '||',
                                                                                )
                                                                                .includes(
                                                                                    levelThree.label,
                                                                                ) &&
                                                                            !imCrew ? (
                                                                                <>
                                                                                    {/*<SeaLogsButton
                                                                                        text={`Disable`}
                                                                                        type="secondary"
                                                                                        color="rose"
                                                                                        action={deactivateCustomisedLevelThreeComponent(
                                                                                            component,
                                                                                            levelThree,
                                                                                        )}
                                                                                    />*/}
                                                                                    <Button
                                                                                        onPress={deactivateCustomisedLevelThreeComponent(
                                                                                            component,
                                                                                            levelThree,
                                                                                        )}
                                                                                        className={
                                                                                            'border border-destructive text-destructive hover:bg-red-vivid-50 rounded-md px-5 py-2'
                                                                                        }>
                                                                                        Disable
                                                                                    </Button>
                                                                                </>
                                                                            ) : (
                                                                                <>
                                                                                    {!imCrew && (
                                                                                        <>
                                                                                            {/*<SeaLogsButton
                                                                                                text={`Enable`}
                                                                                                type="secondary"
                                                                                                color="sky"
                                                                                                action={activateCustomisedLevelThreeComponent(
                                                                                                    component,
                                                                                                    levelThree,
                                                                                                )}
                                                                                            />*/}
                                                                                            <Button
                                                                                                onPress={activateCustomisedLevelThreeComponent(
                                                                                                    component,
                                                                                                    levelThree,
                                                                                                )}
                                                                                                className={
                                                                                                    'border rounded-[6px] bg-light-blue-vivid-900 border-light-blue-vivid-900 text-popover shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-background hover:text-light-blue-vivid-900 px-5 py-2'
                                                                                                }>
                                                                                                Enable
                                                                                            </Button>
                                                                                        </>
                                                                                    )}
                                                                                </>
                                                                            )}
                                                                        </div>
                                                                    ),
                                                            )}
                                                    </div>
                                                )}
                                            </div>
                                        ),
                                    )}
                            </>
                        )}
                        <div className="col-span-3 md:col-span-2 p-4 pb-0 1">
                            <CheckFieldTopContent className="justify-start" />
                            {component.customisedComponentFields.nodes
                                .filter(
                                    (
                                        customFields: any,
                                        index: number,
                                        self: any[],
                                    ) =>
                                        self.findIndex(
                                            (c: any) =>
                                                c.fieldName ===
                                                customFields.fieldName,
                                        ) === index,
                                )
                                .filter((field: any) =>
                                    customisedComponentFieldsCombinedFilter(
                                        field,
                                    ),
                                )
                                .sort(sortCustomisedComponentFields)
                                .map((field: any, index: any) => (
                                    <div
                                        key={field.id}
                                        className={`${filterFieldClasses(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tab) && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                        <CheckFieldContent>
                                            <div
                                                className={`flex flex-nowrap border-b border-cool-grey-100 gap-[10px] standard:h-12 ${subCategoryVisibilityCheck(component, tab?.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${component.active ? '' : 'pointer-events-none opacity-50'} field-${getCategory(field, slallFields, tab)}`}>
                                                {isRequiredField(field) ? (
                                                    <div className="flex text-red-vivid-600 items-center">
                                                        *This is a must have
                                                        field
                                                    </div>
                                                ) : (
                                                    <div className={`flex`}>
                                                        <RadioGroup
                                                            variant="horizontal"
                                                            className={cn({
                                                                'opacity-60':
                                                                    imCrew,
                                                            })}
                                                            gap={'none'}
                                                            value={
                                                                !getNoCheckedStatus(
                                                                    field,
                                                                    updatedFields,
                                                                    updatedLocalFields,
                                                                )
                                                                    ? 'yes'
                                                                    : 'no'
                                                            }
                                                            onValueChange={(
                                                                value,
                                                            ) => {
                                                                if (
                                                                    value ===
                                                                    'yes'
                                                                ) {
                                                                    updateFieldStatus(
                                                                        field,
                                                                        'Required',
                                                                    )
                                                                } else if (
                                                                    value ===
                                                                    'no'
                                                                ) {
                                                                    updateFieldStatus(
                                                                        field,
                                                                        'Off',
                                                                    )
                                                                }
                                                            }}
                                                            disabled={imCrew}>
                                                            {/* No Option - Destructive */}
                                                            <div
                                                                className={cn(
                                                                    'flex w-[48px] bg-red-vivid-50 justify-center py-3 standard:p-0 standard:items-center',
                                                                )}>
                                                                <RadioGroupItem
                                                                    value="no"
                                                                    id={`${field.id}-no_radio`}
                                                                    variant="destructive"
                                                                    size="lg"
                                                                />
                                                            </div>

                                                            {/* Yes Option - Success */}
                                                            <div
                                                                className={cn(
                                                                    'flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center',
                                                                )}>
                                                                <RadioGroupItem
                                                                    value="yes"
                                                                    id={`${field.id}-yes_radio`}
                                                                    variant="success"
                                                                    size="lg"
                                                                />
                                                            </div>
                                                        </RadioGroup>
                                                    </div>
                                                )}
                                                <div className="flex items-center">
                                                    <Button
                                                        className="text-left break-word cursor-pointer "
                                                        onPress={() => {
                                                            setCurrentField(
                                                                field,
                                                            ),
                                                                setOpenConfigEditDialog(
                                                                    true,
                                                                ),
                                                                handleEditorChange(
                                                                    field?.description,
                                                                )
                                                        }}>
                                                        {field?.customisedFieldTitle
                                                            ? field.customisedFieldTitle
                                                            : getFieldName(
                                                                  field,
                                                                  slallFields,
                                                              )}
                                                    </Button>
                                                    {field?.description && (
                                                        <SeaLogsButton
                                                            icon="alert"
                                                            className="w-6 h-6 sup -mt-2 ml-0.5"
                                                            action={() => {
                                                                setDescriptionPanelContent(
                                                                    field.description,
                                                                )
                                                                setOpenDescriptionPanel(
                                                                    true,
                                                                )
                                                                setDescriptionPanelHeading(
                                                                    field?.customisedFieldTitle
                                                                        ? field.customisedFieldTitle
                                                                        : getFieldName(
                                                                              field,
                                                                              slallFields,
                                                                          ),
                                                                )
                                                            }}
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                            {field?.fieldType === 'files' ||
                                                (isFileField(
                                                    field,
                                                    slallFields,
                                                    tab?.replace(
                                                        'Logbook',
                                                        'LogBook',
                                                    ),
                                                ) && (
                                                    <div
                                                        className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                        <div></div>
                                                        <div className="colspan-3">
                                                            <DocumentField
                                                                documents={
                                                                    documents
                                                                }
                                                                setDocuments={
                                                                    setDocuments
                                                                }
                                                                fileUploadText="Policies"
                                                            />
                                                        </div>
                                                    </div>
                                                ))}
                                        </CheckFieldContent>
                                    </div>
                                ))}
                            {getFieldsNotInConfig(
                                component,
                                slallFields,
                                logBookConfig,
                            )
                                .filter((field: any) =>
                                    customisedComponentFieldsCombinedFilter(
                                        field,
                                    ),
                                )
                                // .filter((field: any) => filterFieldByVesselType(field, slallFields, vesselTypes, vessel))
                                // ?.filter((field: any) => !getFieldGroup(field, slallFields, tab))
                                // .filter((field: any) => getGroupFields(field.value, slallFields, tab, logBookConfig) == false)
                                .map((field: any, index: number) => (
                                    <div
                                        key={field.value + '_' + index}
                                        className={`${filterFieldClasses(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === (field?.fieldSet ? field.fieldSet : 'Other') && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                        <div
                                            className={`flex flex-nowrap border-b border-cool-grey-100 gap-[10px] standard:h-12 ${subCategoryVisibilityCheck(component, tab?.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} `}>
                                            {isRequiredField({
                                                ...field,
                                                localID: index,
                                                customisedLogBookComponentID:
                                                    component.id,
                                                status: 'Required',
                                            }) ? (
                                                <div className="flex text-red-vivid-600 items-center">
                                                    *This is a must have field
                                                </div>
                                            ) : (
                                                <div className={`flex`}>
                                                    <RadioGroup
                                                        variant="horizontal"
                                                        className={cn({
                                                            'opacity-60':
                                                                imCrew,
                                                        })}
                                                        gap={'none'}
                                                        value={
                                                            !getNoCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    status: 'Off',
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )
                                                                ? 'yes'
                                                                : 'no'
                                                        }
                                                        onValueChange={(
                                                            value,
                                                        ) => {
                                                            if (
                                                                value === 'yes'
                                                            ) {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Required',
                                                                )
                                                            } else if (
                                                                value === 'no'
                                                            ) {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Off',
                                                                )
                                                            }
                                                        }}
                                                        disabled={imCrew}>
                                                        {/* No Option - Destructive */}
                                                        <div
                                                            className={cn(
                                                                'flex w-[48px] bg-red-vivid-50 justify-center py-3 standard:p-0 standard:items-center',
                                                            )}>
                                                            <RadioGroupItem
                                                                value="no"
                                                                id={`default_field-${index}-no_radio`}
                                                                variant="destructive"
                                                                size="lg"
                                                            />
                                                        </div>

                                                        {/* Yes Option - Success */}
                                                        <div
                                                            className={cn(
                                                                'flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center',
                                                            )}>
                                                            <RadioGroupItem
                                                                value="yes"
                                                                id={`default_field-${index}-yes_radio`}
                                                                variant="success"
                                                                size="lg"
                                                            />
                                                        </div>
                                                    </RadioGroup>
                                                    {/*<div
                                                        className={
                                                            classes.radio
                                                        }>
                                                        <Input
                                                            id={`default_field-${index}-no_radio`}
                                                            type="radio"
                                                            disabled={imCrew}
                                                            name={`default_field-${index}-radio`}
                                                            className={
                                                                classes.radioInput
                                                            }
                                                            checked={getNoCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    status: 'Off',
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )}
                                                            onChange={() => {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Off',
                                                                )
                                                            }}
                                                        />
                                                        <Label
                                                            htmlFor={`default_field-${index}-no_radio`}
                                                            className={
                                                                classes.radioLabel
                                                            }>
                                                            {' '}
                                                            No{' '}
                                                        </Label>
                                                    </div>
                                                    <div
                                                        className={
                                                            classes.radio
                                                        }>
                                                        <Input
                                                            id={`default_field-${index}-yes_radio`}
                                                            type="radio"
                                                            disabled={imCrew}
                                                            name={`default_field-${index}-radio`}
                                                            className={
                                                                classes.radioInput
                                                            }
                                                            checked={getYesCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    customisedLogBookComponentID:
                                                                        component.id,
                                                                    status: 'Required',
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )}
                                                            onChange={() => {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Required',
                                                                )
                                                            }}
                                                        />
                                                        <Label
                                                            htmlFor={`default_field-${index}-yes_radio`}
                                                            className={
                                                                classes.radioLabel
                                                            }>
                                                            {' '}
                                                            Yes{' '}
                                                        </Label>
                                                    </div>*/}
                                                </div>
                                            )}
                                            <Button
                                                className="text-left break-word"
                                                onPress={() => {
                                                    toast.error(
                                                        'You can rename this field after setting its value to either "Yes" or "No."',
                                                    )
                                                }}>
                                                {getFieldName(
                                                    field,
                                                    slallFields,
                                                )}
                                            </Button>
                                        </div>
                                        {field?.fieldType === 'files' && (
                                            <div
                                                className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                <div></div>
                                                <div className="colspan-3">
                                                    <DocumentField
                                                        documents={documents}
                                                        setDocuments={
                                                            setDocuments
                                                        }
                                                        fileUploadText="Policies"
                                                        showFileUpload={!imCrew}
                                                    />
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                        </div>
                        <div className="col-span-3 p-4">
                            {filterFieldsWithGroup(
                                component.customisedComponentFields.nodes.filter(
                                    (
                                        customFields: any,
                                        index: number,
                                        self: any[],
                                    ) =>
                                        self.findIndex(
                                            (c: any) =>
                                                c.fieldName ===
                                                customFields.fieldName,
                                        ) === index,
                                ),
                                component.componentClass,
                            ).map((field: any, index: number) => (
                                <div
                                    key={field.id}
                                    className={`${subCategoryVisibilityCheck(component, tab?.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook')) && checkLevelThreeGroup(field) ? '' : 'hidden') : ''} border rounded-lg mb-4 border-cool-grey-100`}>
                                    <div
                                        className={`px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} field-${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} md:!grid-cols-3 lg:!grid-cols-3`}>
                                        <div className="flex flex-nowrap">
                                            <div className="flex items-center pr-4">
                                                {field?.id > 0 ? (
                                                    <Button
                                                        className="text-left break-word cursor-pointer"
                                                        onPress={() => {
                                                            setCurrentField(
                                                                field,
                                                            ),
                                                                setOpenConfigEditDialog(
                                                                    true,
                                                                ),
                                                                handleEditorChange(
                                                                    field?.description,
                                                                )
                                                        }}>
                                                        {field?.customisedFieldTitle
                                                            ? field.customisedFieldTitle
                                                            : getFieldName(
                                                                  field,
                                                                  slallFields,
                                                              )}
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        className="text-left break-word cursor-pointer"
                                                        onPress={() => {
                                                            toast.error(
                                                                'You can rename this group after setting its value to either "Yes" or "No."',
                                                            )
                                                        }}>
                                                        {field?.customisedFieldTitle
                                                            ? field.customisedFieldTitle
                                                            : getFieldName(
                                                                  field,
                                                                  slallFields,
                                                              )}
                                                    </Button>
                                                )}
                                                {field?.description && (
                                                    <SeaLogsButton
                                                        icon="alert"
                                                        className="w-6 h-6 sup -mt-2 ml-0.5"
                                                        action={() => {
                                                            setDescriptionPanelContent(
                                                                field.description,
                                                            )
                                                            setOpenDescriptionPanel(
                                                                true,
                                                            )
                                                            setDescriptionPanelHeading(
                                                                field?.customisedFieldTitle
                                                                    ? field.customisedFieldTitle
                                                                    : getFieldName(
                                                                          field,
                                                                          slallFields,
                                                                      ),
                                                            )
                                                        }}
                                                    />
                                                )}
                                            </div>
                                            {
                                                /*isRequiredField({
                                                ...field,
                                                customisedLogBookComponentID:
                                                    component.id,
                                            }) ? (
                                                <div className="">
                                                    *This is a must have
                                                            group
                                                    &nbsp;
                                                </div>
                                            ) : (*/
                                                <div
                                                    className={` ${field?.noCheck ? 'hidden' : ''}`}>
                                                    <RadioGroup
                                                        variant="horizontal"
                                                        className={cn({
                                                            'opacity-60':
                                                                imCrew,
                                                        })}
                                                        gap={'large'}
                                                        value={
                                                            !getNoCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    customisedLogBookComponentID:
                                                                        component.id,
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )
                                                                ? 'yes'
                                                                : 'no'
                                                        }
                                                        onValueChange={(
                                                            value,
                                                        ) => {
                                                            if (
                                                                value === 'yes'
                                                            ) {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Required',
                                                                )
                                                            } else if (
                                                                value === 'no'
                                                            ) {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Off',
                                                                )
                                                            }
                                                        }}
                                                        disabled={imCrew}>
                                                        {/* No Option - Destructive */}
                                                        <div
                                                            className={cn(
                                                                'flex w-[48px] justify-center items-center py-3 standard:p-0 standard:items-center',
                                                            )}>
                                                            <RadioGroupItem
                                                                value="no"
                                                                id={`${index}-group-no_radio`}
                                                                variant="destructive"
                                                                size="lg"
                                                            />
                                                            <div className="px-2">
                                                                No
                                                            </div>
                                                        </div>

                                                        {/* Yes Option - Success */}
                                                        <div
                                                            className={cn(
                                                                'flex w-[48px] justify-center items-center pl-4 standard:p-0 standard:items-center',
                                                            )}>
                                                            <RadioGroupItem
                                                                value="yes"
                                                                id={`${index}-group-yes_radio`}
                                                                variant="success"
                                                                size="lg"
                                                            />
                                                            <div className="px-2">
                                                                Yes
                                                            </div>
                                                        </div>
                                                    </RadioGroup>
                                                    {/*<div
                                                        className={
                                                            classes.radio
                                                        }>
                                                        <Input
                                                            id={`${index}-group-no_radio`}
                                                            type="radio"
                                                            disabled={imCrew}
                                                            name={`${index}-group-radio`}
                                                            className={
                                                                classes.radioInput
                                                            }
                                                            checked={getNoCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    customisedLogBookComponentID:
                                                                        component.id,
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )}
                                                            onChange={() => {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Off',
                                                                )
                                                            }}
                                                        />
                                                        <Label
                                                            htmlFor={`${index}-group-no_radio`}
                                                            className={
                                                                classes.radioLabel
                                                            }>
                                                            {' '}
                                                            No{' '}
                                                        </Label>
                                                    </div>
                                                    <div
                                                        className={
                                                            classes.radio
                                                        }>
                                                        <Input
                                                            id={`${index}-group-yes_radio`}
                                                            type="radio"
                                                            disabled={imCrew}
                                                            name={`${index}-group-radio`}
                                                            className={
                                                                classes.radioInput
                                                            }
                                                            checked={getYesCheckedStatus(
                                                                {
                                                                    ...field,
                                                                    localID:
                                                                        index,
                                                                    customisedLogBookComponentID:
                                                                        component.id,
                                                                },
                                                                updatedFields,
                                                                updatedLocalFields,
                                                            )}
                                                            onChange={() => {
                                                                updateFieldStatus(
                                                                    {
                                                                        ...field,
                                                                        customisedLogBookComponentID:
                                                                            component.id,
                                                                    },
                                                                    'Required',
                                                                )
                                                            }}
                                                        />
                                                        <Label
                                                            htmlFor={`${index}-group-yes_radio`}
                                                            className={
                                                                classes.radioLabel
                                                            }>
                                                            {' '}
                                                            Yes{' '}
                                                        </Label>
                                                    </div>*/}
                                                </div>
                                                /*)*/
                                            }
                                        </div>
                                        <CheckFieldTopContent className="justify-start" />
                                        <div
                                            className={`md:col-span-2 pb-4 ${
                                                getNoCheckedStatus(
                                                    {
                                                        ...field,
                                                        localID: index,
                                                        customisedLogBookComponentID:
                                                            component.id,
                                                    },
                                                    updatedFields,
                                                    updatedLocalFields,
                                                )
                                                    ? 'opacity-50'
                                                    : ''
                                            }`}>
                                            {getGroupFields(
                                                field.fieldName,
                                                slallFields,
                                                tab?.replace(
                                                    'Logbook',
                                                    'LogBook',
                                                ),
                                                logBookConfig,
                                            ).map(
                                                (
                                                    groupField: any,
                                                    index: number,
                                                ) => (
                                                    <div
                                                        key={
                                                            groupField.value +
                                                            '_' +
                                                            index
                                                        }
                                                        className={`flex flex-nowrap border-b border-cool-grey-100 gap-[10px] standard:h-12 ${groupField?.id} ${component.active ? '' : 'pointer-events-none opacity-50 77'} ${getCategory(groupField, slallFields, tab?.replace('Logbook', 'LogBook'))}`}>
                                                        {isRequiredField(
                                                            groupField,
                                                        ) ? (
                                                            <div className="flex">
                                                                {' '}
                                                                *This is a must
                                                                have field{' '}
                                                            </div>
                                                        ) : (
                                                            <div
                                                                className={`flex`}>
                                                                <RadioGroup
                                                                    variant="horizontal"
                                                                    className={cn(
                                                                        {
                                                                            'opacity-60':
                                                                                imCrew,
                                                                        },
                                                                    )}
                                                                    gap={'none'}
                                                                    value={
                                                                        !getNoCheckedStatus(
                                                                            groupField,
                                                                            updatedFields,
                                                                            updatedLocalFields,
                                                                        )
                                                                            ? 'yes'
                                                                            : 'no'
                                                                    }
                                                                    onValueChange={(
                                                                        value,
                                                                    ) => {
                                                                        if (
                                                                            value ===
                                                                            'yes'
                                                                        ) {
                                                                            updateFieldStatus(
                                                                                groupField,
                                                                                'Required',
                                                                            )
                                                                        } else if (
                                                                            value ===
                                                                            'no'
                                                                        ) {
                                                                            updateFieldStatus(
                                                                                groupField,
                                                                                'Off',
                                                                            )
                                                                        }
                                                                    }}
                                                                    disabled={
                                                                        imCrew
                                                                    }>
                                                                    {/* No Option - Destructive */}
                                                                    <div
                                                                        className={cn(
                                                                            'flex w-[48px] bg-red-vivid-50 justify-center py-3 standard:p-0 standard:items-center',
                                                                        )}>
                                                                        <RadioGroupItem
                                                                            value="no"
                                                                            id={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-no_radio`}
                                                                            variant="destructive"
                                                                            size="lg"
                                                                        />
                                                                    </div>

                                                                    {/* Yes Option - Success */}
                                                                    <div
                                                                        className={cn(
                                                                            'flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center',
                                                                        )}>
                                                                        <RadioGroupItem
                                                                            value="yes"
                                                                            id={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-yes_radio`}
                                                                            variant="success"
                                                                            size="lg"
                                                                        />
                                                                    </div>
                                                                </RadioGroup>
                                                                {/*<div
                                                                    className={
                                                                        classes.radio
                                                                    }>
                                                                    <Input
                                                                        id={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-no_radio`}
                                                                        type="radio"
                                                                        disabled={
                                                                            imCrew
                                                                        }
                                                                        name={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-radio`}
                                                                        className={
                                                                            classes.radioInput
                                                                        }
                                                                        checked={getNoCheckedStatus(
                                                                            groupField,
                                                                            updatedFields,
                                                                            updatedLocalFields,
                                                                        )}
                                                                        onChange={() => {
                                                                            updateFieldStatus(
                                                                                groupField,
                                                                                'Off',
                                                                            )
                                                                        }}
                                                                    />
                                                                    <Label
                                                                        htmlFor={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-no_radio`}
                                                                        className={
                                                                            classes.radioLabel
                                                                        }>
                                                                        No
                                                                    </Label>
                                                                </div>
                                                                <div
                                                                    className={
                                                                        classes.radio
                                                                    }>
                                                                    <Input
                                                                        id={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-yes_radio`}
                                                                        type="radio"
                                                                        disabled={
                                                                            imCrew
                                                                        }
                                                                        name={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-radio`}
                                                                        className={
                                                                            classes.radioInput
                                                                        }
                                                                        checked={getYesCheckedStatus(
                                                                            groupField,
                                                                            updatedFields,
                                                                            updatedLocalFields,
                                                                        )}
                                                                        onChange={() => {
                                                                            updateFieldStatus(
                                                                                groupField,
                                                                                'Required',
                                                                            )
                                                                        }}
                                                                    />
                                                                    <Label
                                                                        htmlFor={`${groupField?.id ? groupField.id : groupField.value + '_' + index}-yes_radio`}
                                                                        className={
                                                                            classes.radioLabel
                                                                        }>
                                                                        {' '}
                                                                        Yes{' '}
                                                                    </Label>
                                                                </div>*/}
                                                            </div>
                                                        )}
                                                        <div className="flex items-center">
                                                            {groupField?.id >
                                                            0 ? (
                                                                <Button
                                                                    className="text-left break-word cursor-pointer"
                                                                    onPress={() => {
                                                                        setCurrentField(
                                                                            groupField,
                                                                        ),
                                                                            setOpenConfigEditDialog(
                                                                                true,
                                                                            ),
                                                                            handleEditorChange(
                                                                                groupField?.description,
                                                                            )
                                                                    }}>
                                                                    {groupField?.customisedFieldTitle
                                                                        ? groupField.customisedFieldTitle
                                                                        : getFieldName(
                                                                              groupField,
                                                                              slallFields,
                                                                          )}
                                                                </Button>
                                                            ) : (
                                                                <Button
                                                                    className="text-left break-word cursor-pointer"
                                                                    onPress={() => {
                                                                        toast.error(
                                                                            'You can rename this field after setting its value to either "Yes" or "No."',
                                                                        )
                                                                    }}>
                                                                    {groupField?.customisedFieldTitle
                                                                        ? groupField.customisedFieldTitle
                                                                        : getFieldName(
                                                                              groupField,
                                                                              slallFields,
                                                                          )}
                                                                </Button>
                                                            )}
                                                            {groupField?.description && (
                                                                <SeaLogsButton
                                                                    icon="alert"
                                                                    className="w-6 h-6 sup -mt-2 ml-0.5"
                                                                    action={() => {
                                                                        setDescriptionPanelContent(
                                                                            groupField.description,
                                                                        )
                                                                        setOpenDescriptionPanel(
                                                                            true,
                                                                        )
                                                                        setDescriptionPanelHeading(
                                                                            groupField?.customisedFieldTitle
                                                                                ? groupField.customisedFieldTitle
                                                                                : getFieldName(
                                                                                      groupField,
                                                                                      slallFields,
                                                                                  ),
                                                                        )
                                                                    }}
                                                                />
                                                            )}
                                                        </div>
                                                        <div></div>
                                                        {groupField?.fieldType ===
                                                            'files' ||
                                                            (isFileField(
                                                                groupField,
                                                                slallFields,
                                                                tab?.replace(
                                                                    'Logbook',
                                                                    'LogBook',
                                                                ),
                                                            ) && (
                                                                <div
                                                                    className={`group grid py-1.5 items-start  ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(groupField, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === groupField.fieldSet ? '' : 'hidden') : ''}`}>
                                                                    <div>
                                                                        <DocumentField
                                                                            documents={
                                                                                documents
                                                                            }
                                                                            setDocuments={
                                                                                setDocuments
                                                                            }
                                                                            fileUploadText="Policies"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            ))}
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                    {field?.fieldType === 'files' ||
                                        (isFileField(
                                            field,
                                            slallFields,
                                            tab,
                                        ) && (
                                            <div
                                                className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                <div></div>
                                                <div className="colspan-3">
                                                    <DocumentField
                                                        documents={documents}
                                                        setDocuments={
                                                            setDocuments
                                                        }
                                                        fileUploadText="Policies"
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            {logBookConfig && (
                <ComponentsNotInConfig
                    tab={tab}
                    logBookConfig={logBookConfig}
                    slallFields={slallFields}
                    imCrew={imCrew}
                    createCustomisedComponent={createCustomisedComponent}
                />
            )}
        </div>
    )
}
